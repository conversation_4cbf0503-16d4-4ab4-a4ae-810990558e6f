MOONSEC V3 MALWARE ANALYSIS - COMPREH<PERSON><PERSON>VE SECURITY REPORT
======================================================================

EXECUTIVE SUMMARY:
This file contains MoonSec V3 obfuscated malware. DO NOT EXECUTE.

THREAT LEVEL: HIGH

TECHNICAL ANALYSIS:
============================================================
MOONSEC V3 LUA MALWARE ANALYSIS REPORT
============================================================

1. BASIC STRUCTURE ANALYSIS:
------------------------------
PROTECTION: MoonSec V3
VARIABLES:
  _zEFilpuGzsUZ: a
  twuTsJpSydrWUjTa: _ENV
  bgVgESx_HBsUnea: 'yPBv6n0xLAdbZ1Dzzzd6x06LP1z6bDAm0BdL0xvdD1DbbdLz06Z0A00Lvd5Z1Db8xBAvBAzzZdAZ0DA6xxnZBdZdZAd6VP1vdBx6600nnvBdAP0xvBK610DdDzZEABnzvRbn0AnzNd1ZdD16bdL10ADL*S1nddxniBBbDxZb6d0Z6W(zD0dAxAnZPzz9xDdx0b6L=DALAxLbn1BDDnZAAABLvk1zLPnbBBz6Z0Dn1zdLLB6ABnDAZBL1n16sdPx06BP6D0
  d1dZZAxn: B6z6ZxALxxvZ3DzbbBLLn0BLzdZzADd}61P6DdbLbxnZn0gID6bvx061PdPxbDAp0Bn6P01LdzxZnvBgqnZ6dLxnvdPB1DZbLBn6B0zLzDAZxv6{P6D6bLLL0d6vzD16dBxn60P1DdDZAb0+v0o6D0dLAb6ZPDz0ZBA000vZ7d1DdDdenLB6zAZLAZ0ZnqPFDBZBL0n1BdzZZDdvxBx6PxDLbzLZLbvMiZ1610L66dBPDD1PABL6v07dD6dZL6nVB6z6Z0ALxd6vYDDnbBdDn0BDzdZZbdxK6nP6DbbLLbnZ6Dmx1Bdxx06dPdDzbDA{xjv6kb1LddxZnCBqBBZDA001vdvA1D1vLBn0v5zL1jAZxB6{PBD6Z0dLndvPzDzddBxd60PL-nbZAP0lvxt61xdLAd6zPDzvZBA0006D)dDDZxLunxB6PLZLAd0ZvDP6DBbxL0n1Bdz1ZDZmxZ66PADLbZLZxvvtP6Dnd0x16dvDDDZ2AB060nWL11dZLvnOBvz6D0Z10dvzmDDBbBd1n0BLkdZZdvxm6BP6DZbLbd01BD?n1B1Px00BPdPZbzAV0xv6=d1LdZxZxDBxzBZAA0L0vdvP1DDKL0n6BbzLDDAZx9626BzPb0L1ndnBzD1BdBx0n_PLz%bZAP0rvBp6D0dzxdnPPDPdZBAd00vLPv1ZbPLsnxB6zxZLbdx6vDPvDBb0L006BdzZ1bdixx66P0DLbZLZLD6TeB1Ad0dn6dBPDDZBAZ06vZCL1ddZxDn4vBzbZ0A10d00{DD0bBL60nBLz1ZZdvxq6vP6l0bALdnzBD-B1Bdzx06LBdDZZvA 0Bv6#b1L1dLd6DBnzBzPA0xvvdvZDdb
  ELoDtKcZvbbaKclho: '
STRING_COUNT: 23

2. STRING TABLE ANALYSIS:
------------------------------
MAIN_ENCODED: 3330 characters
UNIQUE_CHARS: 82 items
  Sample: [' ', '!', '#']
CHAR_FREQUENCY: 82 items

3. CONTROL FLOW ANALYSIS:
------------------------------
FUNCTION_CALLS: 20 found
  Examples: ['gsub', 'S1nddxniBBbDxZb6d0Z6W', '0b1dPxPvz', 'xB6nP0PL1zLZ0Pv', 'spB']
CONTROL_STRUCTURES: 1264 found
  Examples: ['while', 'while', 'while', 'if', 'if']
STRING_OPERATIONS: 4 found
  Examples: ['gsub', 'sub', 'byte', 'byte']

4. SECURITY THREAT ANALYSIS:
------------------------------
⚠️  SUSPICIOUS PATTERNS DETECTED:
  • getfenv: 3 occurrences
  • _ENV: 3 occurrences

5. BYTECODE ANALYSIS:
------------------------------
Potential bytecode chunks found: 50
Sample chunks: ['yPBv', '6n0x', 'LAdb']

ADVANCED ANALYSIS:
{
  "main_string_length": 3330,
  "lookup_string_length": 45654,
  "potential_decoded_strings": [
    ";+!ZCGBd^>4?w8p7?>7ZC>>+8G+^+>>p7CZCd-w^9CG+>d>^+^!4dZ?4L+CZw8+dG!>dp>pC^t^8WGC7^4w7!++w4>wGZ8^dw!%dG>GCp878dG?774Z7>+^w!!ZG487dZ!ddw>wCG7B8pG!7B447T+7w^74G+8Bd4!pdZ>ZCwj87+?BC?:p7Z4^d77C>^Cw>+?+B4?w+7?C8wGv7C4^7p+8wBd^d*wGGG488+GG84dpdp>dp>Z8B+?Gp>Z+pdjB^78+Z^?^78w!8?Ap^Z<d4?4?wG+BB4+ZnZ+447Z7Cdw8Wy8+g>w4Z7G^C^Gw8+C^>4?pBdGdG?wc?d8^63?G8GGp4lod^dww>77^?448wB0B7?7 GC?^dw4!0BB4w7CC B7.!Cw^dww+p+>4^wCZwCqw+wZGZ>d41!C!?d8Ew+ZG>w48>ZGBp78cFCB?pw+Gw>d8?!?!C?",
    "w7k+^G>+8pB8B7?>7pZ4d8w7GpBC4Cp?B^B>6dZ7^d8++++Z4^?87^^d^^8G+CC?8Z!dBCBd7Z74Cw8?88GC>>p!t!Z8wwwwCdGp4B8dB!?C4wC+Gdw78Z!vGBp!D+ZZ?dwB+BG44Z8p!+dC?8L^C>>BwBG>4Z8p!ZdGd6:?+?>Z>d+^+444pp7dd?^!8CBZBB4p!CZ7dd8Z+>GZp!pZ!44Cp>p8^^wZRdGdGf8w7?!8?+wPz4>>>?pCGwBB4>7?^4^?6B+4B+>G!^d!487!CCZ7w?p4!BB8?>74CCddgdC?Cwp+!>dGB^7ZZ+^!w++7G4>ppppdd!?Je+C%Bd4+Z{Z)BB5!++^78++dBG4CpT!4dp^p77C7^w^7+?B>?d7DZ?d7^9Lw+ZBd74rEZ4d+(wCd^?8?8CBbd7?+ZGCC^p+8G+BC4ZpBZ^wdw^Zp^^4Z8!BG47p4!7^+dwl^!dBG?U7!Z4d7?CCC^^^>+pBB?!4GZ1Bp?77pGw>d8?!?!C47p8Zpd84C8pB8B84ZZ>Cp^jf8GG4+>C!4!?447pZ?ZpwC7+C+>W4+Zd^+dB-w+7>jp5+8!Y4w74C^^+^ZV7!pBfpC79Z8www8+?BL>>8w!p?8?7Z^d?>Gw^G8dZp?p7Zwd!<GC7^4w7!++w4^wdDBCp>A84+8GCpC!^!>?p#BG!CG8MvpG7>p7wZdd?w?wCC7^88p+8CCdp=8z8CZ8BppdZ?ww+Z!^!?7w!Cp>p8?8pB4?47d74^B8C!!G?4Bp4p?dddp8!BGB?4G8wdd?!7BCBZ7w?p4+w4e4w7>^^^777FpG+4ZZ!ZZ^fw!Z^Z88?+ZG?4GpGpddw>+wwG?G88Z7++74d7?Z^Z?wdwp!t47477^d+?C8B++4a4^+wZ!BCp7C+Z>wppCGwBt48pZB7^pwTCCG+>7p^Z!B?4p1ZCw>GwGG^4!88!!dCB7#!+4>!>B+d+>4>p87Bd4^+8ZB!BC847ZZ^dB8!=?>4>4+CG^B74GCud??7_7E^>C?ZpGB>d^w!G+G!>w+Z+pB+?ZC!CB^78!+pC8pW!4BG447w7d^Z4!pZ!dB7?C78!7?73ZhC>>p+!8!*4?p>Z4d>8d+!GB4B>7!4B^?>7^+7G>p^p^+p4474ZdZ4wB+CB!>?pB!4!??d?p+!4G4?pG+w?d7!ZB^Bd7o?!4Gwp1pwZ>w^w4Ip^>>G8^!4?>?4JZd4?7wCGd4+8w!+dZBp74+>G?4+p+!7d!4BZBd4d?+3G^4C>d!!BJ?+7NCp^>w8+8+B4+87ZRB7>B8PG7G7>G!?C ?+7BCw^Gw?+?B4G?pZZddCddnGC44D8>+7B7?p?d77G^w>+>GBG>pGZZ^+?4?wC^Bd>>+8!4dBnG6BG!wG8!+GBd7B7BZp^?7w:+>B4w!C!^dG4?Zp^^wZ3^G4GGp+q*Z7^G8++BG8^8+8B+B!7dZ7^?dpN>Cd>^8dZGdq?C:C78^^wB+dGBd8?dCBCBdws>!d4CpwZ+B7??7?+B^d8d+C+d4Z7ZZcZZ?7k7CwC78?!>dd?t?!ZpG8^7+Z!SBw7?78C+>+w^U?G8pwp8BC^+7!7d^w4!+4+pB?4+Z7dd?7Q>C>C?pF7GZr?7w+}4GG^Zv?G7>w>7!?Z!^C+Z+?G44^7Z!p?8w/V?d8^+VwGpGBp?_+d>d8w4lf^w4?88B+Bp?7Y^C4^^w?!#B??C7>Z7d)LZC8^^w8+7+44>wGZ8C+w!wCGC>^4+!G!wdpa8M7^d>?pG!+d?wZ78^w^w_d!+>+>dZ!B8?+H+74^B4G8dG?B4?CCZCG^?=Z+_GZ4GZCBCd7wBr^Z78!+wGd>wppp>dG>C8M+pBG4^p7!+?+7G7B^?8Z+7+!>88?!wB?w>KCC^>^>++wG44?p4++C?8484+N>wpw!>!w?^.BGC^88^+w+84>?fLC>d>88d_p4>pC!^d^d+78+wCp8!87!??4?4/Cd>^dw4+w4?4w7dB8^G?BC>>Zwp+ZBGBvpw<?C8>C8B+wB^>^!^BwB8I!C4>B^>+CG!4Zp!Cud??767x^>Z8+!!B+^^w!G+G+^d!!Z!4!7^Z^dCwwS^GG>?>w+7!??G77Z4d78+wwG^dd?+CHZ7^d?8GH>48G+4BwBd7CC4^4wB+pG44dp8pBd?dw.>X?^p48+w!>?Z4wCGd78G+8B+4C>8!^CGwGlpC!C78Zp!B4B7p77pC^8d8B+pG8p_Z%B8dJ.pCB^p8^+^+4?G74CCd?d8{^G4G!pG!7B447U+7w^d4d+7!DB77p78CG^CL?C7G^8?7?C!Bpp7U7CZ>+!ZGp4!7!p?dCw?HGC^>>>G!+d+4p?+CC^pw>spB(G?pdbBdB^GV3Gp4n8^p!B7d.?7CpC8>G8C+?4Gp7!4B7w+?wC^Bd87p9BCB77BZ4Z?wBpB!8G>847d!pwdo!CB>B^7+?Z4?-?>+C^?4+#?Gw4dpCE8Z!?Bw+CC>!pf+>BC4^4>ZCGC>?wdC^BB>wZGd%?CzC78^>4^+dddd84>!4CGdp=^GB4C88!^BwB87^+^G7^?Ew!8B{7>ZCd^w^w+Gd^48?+4C+?+VdCd^Z8?+dBC444?!7Z8wBw^G^>!p^ZqdC?d?}Z^CB>88^B+4Zp87Cd#^!J?+q>v>?+pBp4?4pZ4^^8Br7G4>p>7!4C4>+?8ZpG8>!!?BB44747Zd7>p+dC4>?84!>ddwG7pC>^8^p+>Z>^F4w!8C^^++4GG>>p>p!Bp^8)BZ>^4w>mpB^B>pp78d^8B+^GFG4pdp^ZdwBwG08Cw>Z84GGd4p?Z?d^d?zpG^>Z8^!4!G?48uC^C>>^!d!BdG48ZdB8wd78!C>Z>G!+d+4p?+Z8^8w4w8G>4>pBp>dGwZ++C4Cw8^7d!>48?w5B>G>G+e!dB!4G7d^B^8w7Cd^8>+!BZw?C?^rGZ?wp+^GZ>^p4pGd+>Ew?+CG84Bpw+8487+7!^dw7+?2p>>8d!^BdwG.NCC>C^8+^GB4dpBU8Cd8B8Bnw4G?dpZBC^Z7!+G^6>wpZ!7>w?^77CB?^8C+?B+8C7%Zd^C7%EwG!4!0wZgdBp+78CZ^?78+>Be44+>Z4^+ppW4G+>?J4!?d+877?C+^^7?+dGp4C+dZZd4wCZZC7>Gp.C7d+?G!CZ7?!8ZCEGw4ZppGwd^?7H?d^>C8?!G>C?X7BC!?Vww+ZG>wwp^!pd^p^VddB>4p+C?Bd487^Bd^?81ZwG^>7p>G^d4?w!8Cp>GP!+pBG?n+pZ7^^7Z+BGww^p?Z+>8?>77CB?>84!+^p4>+wZ844wBK8G^wBp!!>dGp!7pCG^77p+4BU4w+4ZBd8wZZBG!>^p!G!dWpi7wCZ^p7w+^G74w+^Zw^!p8I>G_>4b>!wB?8p74Cy^474+BGw4^+BZ^^+p?HdCp>B-d!ZB??+!ZCB^^7^+Z^B4d+CZ/ddw!ZUCw>Z87CwB^477>B^^Cw4+B^C4ipd!84u?wX!Cw?w8?C4Bp?C+7Z?^Zw>Z?Gd>pp4GddZ?>)+dZ>B8wC^BC4?7GBC^twd+C^_>wpZ!w>w?pFcd+^88Z+p^84>p7Zd4>wG-8G!wGpUGZdZp+78CC>c78!+BB8!7BZw4dwZ)>G^wZ87!GB887XOB8>+8^C!Gp4GpwGpd4w+Vdd4>ppGC7dZ?^!CCR^d8ZCcBC4?+GZ+d^?7Z+C8>Zp!C8B>477BB>^wwwZpB+4^+ZZGd^p^nCC?>!eC+7>!?!!{Zw^Zw7ZwBF4G++ZGd?pBc!C^>do!+pBC4w+pZ74ww^6pG4w^pC!?d+pCoOCd>!1c+wB!44+wZ^d7w>Z^GC>?pdGCds?dvGdn>!8BCGBd?a+4ZBdwwCZBG>>7#?!dB7?B!dC^>m7w+7G78+7+!G^G7BCB^Z^B+!B!>74!!pdp???pC4>48d84BB?B7Z7B^!4+8ZGdBZ?^7>C!dpw++GG84!ppZCBB7!C!d7^!+8+^4>4d&%d^w!78C!>C^7!CZ4?7(^I8d7w7+>_p>4?>7Z^!^+wC67>p8Z+4+?4B7^7D^Zw8V^C8>7>4!GCG??wC!GBB8p7BG*>dp?+lZ8w+ww!G>78Z!CBZ^47^+B>VwC+CGCd?p4_dd^w^DG)^>?pG!hBG?d?!Z7>G8G++B>4G7!Z^Z+w2wdC?GBpB!_dB?pY!CGZp8Z",
    "dG^>d?w!8d8?4?8Gy>48G+4BwBd7C+!CCw>+7G7>?7Z!7dww!wZGCGGpCpyB4?Cp7ZC^B^+ >+?>4?47C^Z^!w49wGdpBpB!wBZ7BCRd?wh+!r84!?^Z8^Cw8wZG>4!>^!pdd?p7CZpd>?p+Rz?4+?Bp8ZdwBwG:8CZ8B!WG?4o7!p8d>>^8!B+BW4^8pd+??7BZ?^8^^+GZZ474CC!d4w!nwCwCpp>!pd^47?+Zw^p^B+?C7B?747>CZ?C7>G+G4pG#4B^?+8pCB>B?d8ZBBBGpG7ZZB??++G+^8pG!+Bp?C?G78^&>7{4+?>w>pZdd+wd<7GZ>B^7+?!4dCYZz!C4^EiwGw>>4Gp?ZZd47G+wCp8Z!>B>4G78Z>^Bwww8+G4!?dZ_ZZd7rpT8GG^?!CGp?C7wCz^Zdw+!P4>BpB!Z!7dG?8CC^^^>+7ZCd?487w^?^4Nd++4Z8p!!d!B?_++BC!^>+!Z!Bw7?74C+^^?7C^>78>+wB8B>7GCG^!^G++B+>p4+ZCdp?>7pGzC?p+7B!Z??4pCw^Cww+BGBG^pCO!dwd7w?G4G>4+8dB!?C4wC+dG>+!*+7B^84+7B7478BCwGGpWyGBZ4w8>C+>+?!wpB+B%pQppZ+?BAwCw^^pg+wB>47?I7^d?>4nG+B>d>>Z!Bww!c4Cp>+^4+B!GB77p78CGd?MdGd>C4SpB!pdG7d+d^G>Z87BpB8?Gp?d??7whC?GZ4GZCZZdwwp+GC78p88!CG?7BZ+dGwG?pC>B>4ZZ!Z+d^47C!C?847GC+>p8dC+G+4BwCG+^G4+ZA!7ddp4&sCZw48++^G74BZZBd^Z+!++G?ww++B?>7??C4C>>!=CC>BG7C7ZZ44p7GCp>^^8pCdZd!?>7>C+^p+8+8BZ4ZpwZ*?87+#8>w>?pGGB>?7G!>CG8C8Z+w^p8Zp7dpd8wGZ^d78^!!+dBp787wCZ^ZwwCpG^>?p8dwdww!z>C>>U8dZm4!w2ZwZw?p84C?>?8^8?Bd?d7C7d>%w4!nB^4wp7p^d?^C1TGZ>d8B%4G^>#7878^4?p84B>B^?O!ZZB?>v>CBG!4?!?!B4??CtGC?>>8C++BZ8p!wB7^pw+G8>B48!!B4BZ8>/4Bp^8qp!wGwp+Z dB>7pBZCd?w8p7B>^37dZZBG^?7Z!d^^w7!>dC^48CC?>!wpA",
    "dG4j4BC4G!^>TB++G^pv!8G8?>p!!>dd>B88!C4??8!+C>?8w^GCd88>+!Zw4U7pCrZ74!8GBZ4Zw+7+G7?788Z+>4p!p^Gd?K7Z+!GB4Z/7!?^^4p!7Zw?8<6Z>G!>+7dBd?C74G+d+w78++p>^87!8dcdZid!TC^?pXGBGd!4?Z4d8>wp(G8>Bp>pZZ!4m8d(dG78?p!C444wGrw^C8?8Z=w4+4^ZpB>dd7dC?BA^?7+ZGB_88!C^Cw>!M<4B48>!pB4??++CZd+pB77BGd+7!7+Z??pw++{G8pZ!4!C^C8++G^p^d74GB4C4ZcBC4wGw8+7BB48+wBZd+7^+ZG44+!>d G44>ZdddwZ24+!^w8Z!BZw4^7?lZB?^ZpZG>>>?d!pCB?^w>G+>^877.d??dw?CPGZ?h7^Gd>pp4F9Z>w+8G+>BB?!84BC^!pp77^dw8+ZB44!?C7?C^?8zG!8^Bw88?GpB+KGCw^C>w7ZBZ^^p^!CZG?!7Z+?^B8^!vCdG7wGOw^!^>8!G^>^?(pBC+?4??74^>>B!w+ddwpwp?^+^w+8!vCp4p!?^)?d?7jB^Zwd7wG^>+77p>d8?^7CG!G44w+p!>4w?d7pdZ4Z=7BaBd8BZB!p^D8GG!^p8GpfCB?Gww!4dw?^!BGC^d?4h+^>dZ8>k+>B>8MwBG?+p+!+C+w+7B!4>>7>p?G>>.8+C4dw^8+GBZ?74>Z7Zdww+Zzw?S?>+h!^?awf+w4+^?+7B^d7w<!CdB>G7!+^G7p2pddG4Z7wZ+C4w8+pCB>C4GZCB!?ppd+zB?pC!+Z8?Zwrv+C48GpdZ!B44?TBC4d^#8DBC8w47CBZ?d7^Z7>?8C+>GC4^ppZdddwZlw+!^w4pS^!^?B7?WpZw?>g-CdBB?G!pdw?^{^CZBG8C!CB?^+8?Cs^wwGhwH7d!p?C+d4>+Q^+>dB4d7++wwswZK4dwwdwp=8dZpZ7?C!4C8BZ7>78Q!9dd4p7dZ8BG^G8!GZ4F?GD7dG?Bwd+^G78^!eCd4h8G6wG?8^*Z+d48wBZ?ZZ?>f+Gdds8G+dCw44pZ!+CpwB<8GC>w?C+!Bd4z7GZ>>ww47w+wddpBtGZ^wG?p+CBG>?pp!+ds7dCZCj4Z+4C+4d?>77Bd?>78GjG^wy!sBd?G74C+GC?^w>G^4B?4ECZCd?u>1>>G?pyG!?BpwC!+GG?B7B!4d*7>77C>4d?^CpGC>?+wG8dp8^C8d^dppC!G>!?Z!>B>?pw7+w^>^p84G847wBZwCd4?+!G!G>?C!v^ZdGQZZ+d+4GxB+7^^8CZC>#?E7aC>>d7Z88CZ4+p^+!Cp?8+!+B4{7!CdBxwGSdRBBB?^+7!!^Gw>CG^B>Z8CB+G4?8-+dd^C8B!G>^84!^B7?778_p^>?>+>Cb4Bpd7CCB?Gw>!+>B4787Zd4BwG7?C^w7Aw!?4Gp!C+G4^p8GG8>G?8+>G!B?7!Z7C0^d!Ze8GCp4++^BdGp8Z7d48C8?Cn>3pwZdGGwwwC+Z>Z?G!^dBw?8CC?4x8^+7Z+d??C+pdG>C8?!GGppCpB!8?4w+Z#^!>B+w!C4?p^!+^7w^M8GGCpp!!?^q?dpePB^wwdpwZZ4Z7++7G7>G7CS8^8w>7BB!4ww?7pC?w^5+B7dG4q+BCd^Z8^+74dppZGBZ?CpZ7CCZ>aw>G^d",
    "pB!BB48rL!ZSBd4?!Cd4>^P^ZBG8>w+>+B4Cp>Z!!4?!8+!8^dpGVZG+dowGfwBBw8p>G4^!?Z!pB>dd7dC?^^>>+7G4Bo7C/dd>w>!%",
    "4^<8>!dB4>wF+Z+C!>+EBBB>748!8Z^>BR>+sBppG{w!w4+8++GG>8CPdGB4j?+Cdd!d^8p2Bdw4Gp7BZ?dww!BCB>p+Cd}dB8nZBdd4G8wZ>^+84+8B7^Bw878Zwwd7>B)^p??9^!8447+CZBGp8!GB?4>8?CQd%^7+hCB?Z8pZ+B7d!p^*XGGwCpp+ww-wZ!cdwwdwpG44!8+p!dd4B?7Z7Gp80+CB>>d7GCw^Zw4+ZC+4.?GppdC?B78lC498dp8Z+4d?C(>C!^>8pG+48>7Z?CC^^+sGddt?d!wCw>Zpwp?Cpwdu8!b>??!8wC!d^w4Z>^84!+4Gp>+pB(GC748?pGB^!pz7B!w4w8p7p^?4Z+Z!?4+?>pdZwwC8GBDG^w6pmBg?G48h8CZ8?p8+pdH?47wC^^w(>+?>G?pp7B??+p+!+GGw++CG!BZp?CDG>d+wB9ddw4B7(Gwdp7d57CG>8m>B>?O?>+aGpwwpR!w^^4C+7G4Bkw/!C^C^4w?G!4Qpa!ZC!4Z7wZ+C44G+p+dddp>!8^K>Bz!BG>mpGp?GZ4+p+C^^+w?!+!ZB4_1!?BNwdwdGZ>?447dZO^!?^+w^84p+>GdG47pD+Gp4ww?uC^+w^7GBC?Cy?G!^>pMuWGpdVpp!dBp?^84!+B^47pGBw4>w?CjZw^48+!4444G!4ZZ^dw4zwC>w8p+BG>np!(CC7w?+u!+4Bprpw^W?ZwkJ^>w8p+?d+?Z74Z>^!447+GdB?48p4Z3>?w>+ZG4w!s>d+dZ78K+^B47bp+8d84Z!7Z+wZw4CC>^>7!+CZd+7BZCdpwp+!G+BG87}>GJ^?7BC7^>8LpCBYBd847ZZww>+GZ8BV>8!B!8dC7>C^^B8_7^BC>ppwZ>G??^w^+/^8?p!4!^?!?BZ8B^^?pEGZdBp^7>dB4?pdGC>4wd!!B>4>?dZpC!?^+>!{>^87!ddw^^wC{?dp4C+BBd4G7GC!BCw48^CCB44B!8B8^!7?++Gd8854!ZBw?4CGZ7?CXw+Bdp8M7ZG4?77dC8>Cwp8w!4>B?>7wdB8+w?C8G>pHp>Zddp?pCZ^Z>++^!>4^p8ZZd?w+p7G8>Www!8!pd!wZ!CC4wpD7GB>78>ZCCZ^>8{GdGC8Gp!d!d?74C+dCwB8BC>B+?8Z^Z>^d{8!7>!>wc7!^?w7+Z8^Cw?c?G>>?pdZ?BZ?dylCw^w4+p>BGB7?Z78^C^^88+d^+47p?Z8wd{+CZG+>788!8?Z?+C+^A>38dGCBBp!+^Z?d47^DGG4>p!Z!w>d?>Z7d8wC+>G?4!w77!G8^Z?7Z7GG8!p!B:47pp+CCG>9 >+g4G>p7eB7?Bw^t?>ww7+BGp48?G!pCC478+F7>Gp4pZBBd!wBC)^7>d8GG?4!pZ7?^.4?8ZC7G888!+dBd577A^>!>BE++>d>4^!pZG?B77C?>!4Bp>dC488^*BCG^4+p!!^!?Z!wdZ>B7?v^G:wG+pB/d>7!7wC4w+8+BZ^?pC7^Z?w+pZx!G^8Cp>!GdC7phGB!4>Vw+ZBp88p^Z44Gw?6dCpwp8>!+?G7+OXd7>+w4G+>!4Gp(Z??C87G+Gd8+7?B+BGp^7C^^w48d+>>!8GpG!4?Zad!C^^wp+^CZBBp?!?dC^88XZ+^Z4>+wB>?pp+X+CBw78pG44Cp.7!BGd8wpGC>Z8w!mZGd+8>ydGB>G:8BB^Z4><!dC>7UZ_>G?pGp>ZN?Gw!!>BGw^pw!%4?4pZCd!?w(i!?Gw>^87d+dC?wCB^!wB7wCZG?8+Zdd>>?/d7pCw>ZpwBC4pp>!p^_d?Qd!BG^4C!4ZZ?48BGZB>?C7-G/4O477Zd^wp%pC44!8p!?d+d!?4CC^pw>ipB2G?pB7Gdp>CU4+Z^78B+!GZ4b7ZCGB+>P+d!)4p7>+Bd>?C.!ZB>C4wpoBG??7?Zd^7w?+^GpG7pZ7+d4dw77J>^8>d!C!>4>?C7p^d^^+G+4>44GpwB7?77wY^Cp8Z!>B>4G78Z>^Bwww8G+G7p>p4d+?G?BC+B!8dp",
    "Cu?47++!d7?8%GC8G+pw!dB????CC8^d88!GB>4w4GZ+ZZ?7f7CwC78474B^dB7pKd>Gpu78BM>?p>+8d?d^E8!ZGGp!pBBpdd7pCZB4wC=8G^>8p^7+ZCw!w?Z4>d8d78C74>0CCpZwww+O+>4B4CZ!Z?w^p8C8B4w+)BB+4B?GM7Gdw88BCp48w!!dGd>^8?C?Cdp!+8B+?+44Z7GG^d+C+44q4>ZZ^L4^/dCGd^8+7^!ZBB7C78^Bw7+^G 4wp>!+B8BpKdC+>d87!ZBBG7p?7Gd>^Z=d+G>74_p4B4dC7^gB>0>+8^G?4>7dZ#Z!w?HGG?4Zpd!4!Z47?+Z8^8w4w8BB4g7BZp^!wG?pC4Cw8^!^BGdC7GC?^?wd+7G?4^ppp7dZ^+u?sw>Z8d8^BZ^C7>v!B84BppBdd>p4Z!dddZKpC>^8888BB74>77Cd^?wpwdGZGGp+Z+Bpd+7w+w^4>^!o!>?d&!+7^!?8z?Z7>8>4!7CGdd1C}d>O>>+ZC+4B8>+8^Z4++!+ZGBpCp8d+4B77! B4?Y78GBGpppZ!Z?w^wBGCG8p?++dw?+7>Z4d78Z8dZ+d>p77^d74^pC!w4B8d!dGCB>JCZ7^Z8ZwwBG>77GZ8^+wC?8C>C?8d!GdZ4w4pCd^+8d+7BZ4B>7!?ZG?>wZCdGG87pH!4?C?CZ^CB818++^>?p?!^!?wC7pGC>wp",
    "!Z+w?!?!C1C&wC8!G!G84d!dZ+?GwZC8Cp>G+^B^4G?+C+d?8++>G840>>!pC!?Z.ZCWCw>!84!+4?7GZ<dGwdw!G+B8pZpwd.4C77GC^ppJ+>BGG87ZZ8Z!wGwpCw>8w4+Pd84>7dCdG48!+wGd>wppp>dw?w8?k8>Z8^!pBd4878CwCB8Z+8+4>77GZGd+w>qGG!>^>>p+d+4?x+C>^88aw>C7dwww+?!w?^T^CGG+44!!Bw4dpwZpZ>82pCG4BZ7+ZwG>?w7dCGd>8dp7Bd?+pwZ+^Zdp+C!>>74^ZOd8>CT>!GB>?e!4ZC4w7BZ+dBw^wZGwBppCp8GZ?G84+7^787!ZG>d>7+Z?dB??f8/^4Z?Z!>Z!wGpdZ4^^?++dB!>??pZB^r??h3G!C884ZAdU4w#CCi^88ZwwB+>74>Z?Z?^Cw?+!GC8p!^BZ4^747Gdp4q+5+>>84?pp^^^!w>+u4B>>+!!74!?C74C+^48+!44!pw!dBw?p?>CpBC84pZG74Bp!!ZdvwZ+GZ+B7pd7LGB??+*G^^wwp+8ZpB>ppCC^Cw)+^GC4+pdp^ZY?4wZ!d>?8?+8C7>Z78CCZw^C+0!B>p4!!4ZZw+",
    "w!>^wwd+GC>4d4CZu^x?8w>GZ>88^+8B7B47dC8^8w>!+G8447kp>dGd7X!++>+>w!ZBdB^7C7B>+ww+(BcG>7!!w^!w49pG+C4878^BC?C7+m7^>>708+?4G?>!4d4?d?4C8>d8!+dB>BCp77p^d^wU4!?>B4C!7Z^?C7?Zdd^wG+^B4^B?GZ+CZwZ8+CpG7pBp7G7w+-?CpBw8Z+dd+B>w!Z4^C?70CGBG+8pZCdC?Yb^CC>+8d8aG8BB4pZ^^!?8)!GCC7pCp>B^^^7p*ddd8>!ZBB^Cp?!7^>^V",
    "++!>!>w!dZ9?pR>!B^>wC+!CB4C48ZCZ8827!CG>Z?w!!B84B?dh+^+^?+B+74!wpZGddw^p?CZdB>w!Z!w^4pGZpBp4wp8BZdw4Bp7dd^)*^GGBwp+78C+^^m!74GZwCwpG>B!p4ZdCpwZ87!ZB4pC8wZG?Zp8CZ^?w7+!+Z>B4Z!wZG?>iG!pd^w>7?Bd?G477^^!>BDdGd>C?!!4dC477CCBC+p!pwB4477>ZkC8wGI7C4^7p+8wd8^d78(B^!w>TGCB>ZpBZ^GCdd54GC^78C!B!+4pcCCC^r8^+CB+4d4}Z!ZBdpK^G!^88!!C+7?Gw4C!CZ^?pCGw4Bp+!Bd^dZ77+p>B>d87Z^?:74ZGd4wwwdB!d!p^7+d7w4pdC4^G8Z%dBGd87GZ7d4?7++yw4!?dp>Bwdd7?H>>S>>!dd!^77!!8d?47 8+B>84B!wBd^7pp!^>B?45B!iB^48Z^C+w>+B!84!?pJ!C>wZ7ACGCp4p+7C74?ww!-Bd4Ow^V7G^pZ7BBp?^7ZZ^^4^G+4Zu>p4>!8Z?w!w?!G^8pw_CB^^7pZ+4C^w78^C44dp>+dG^4n7CGZG!4?!ZB84^p8Z7Z4wBpG+dGp44ZUZ>wd+!!7>!w8+?C748?Z!7dw^d<?+>4#4>I>d444cB!dd?w!7?!ZB4wZZ^dw^G+!!^B7pGppB4?+p8!pd?wp!gZwB^74ZGd>w>w!G!>B>7!8C!^>H8NZ>G8-+CBCG87BZ}^Bwp+!GGCp84pBB7d^7CZ?dd?^iGG^44wB2CdGwZ++C4Cwp+7dB8dB/CC7Bww7F4G^^wp4v!^!>!Y^++^88C+NG+>p7+CZG7>8=pGp>??>!4d^wB77C4^p>d!CZ4^+7dqId!?Cx+Zd47p^m?d+d4RwCd^?8?8CB84d78CG^>www8G?d",
    "?d!8ZB?!7>ZGdBwZ+BB^^C?5!7^Zwow?C7G?4i!?ZCw!O8!4^8w^+BC44^?CZ^C+?8FCC.^+wp!+dZ^7??3!^78>8BG4BB4?ZBZ7??X!Zpd7ww+7d+^8?7CZCww^n7C?^w8>!wdp>4wB:8BB8d!dBC^!eWZ4>K8^+wG7G^p?7C^Cw+I7!B^7?>!?BZB87+77^^^p+C+84+8p!ZG8wCsBCp^>>d87!C?d?G7>^d^pwpG>B!p4ZdCpwZ87!ZB4pC++BBB77Z!Gd^4c7BZ8^>73p>Zfdd8MC+>+wppwBC4pp>!p^Rd?",
    "w!B>p?!!4dC477CCBC+wp!CBC4h7^ZC^+wdwPGCGBpZpGB7?>7CZ>^?^B++!74Cp^7+dpwB+!kG>4>w!pB>48787B^?4++G+4B!??ZydCd8r4!VBd88pBB!4>pG!BdZwB+^ZCBK7!!8d+w+?4GZ^8pZ!?B7?!?!7>GCwG+GG!dFp+Z+Bp^w7w+w>!>?!BC?4>7!+7^d8d7^+C>G4B!BZY??wCG!>8?4+8G^4B84Z^C+w^8+BC^B8>!dG:?B2+Z4C?>G+G+744?ZZBB!w>:?Gwd78d_4Zz?dwy+pd>8!M!C:^+7d+&Z4^Z(8+Z>>4!Zod?>^7?ZB^C?^+B+wdGpB7/d8d73>GZ^p8Z!G!S4ww?7dZpw>+>BZG^p8uZZGw&w>Gd4!?7!!G84?87Z8CG8?8++?G>p+pCB7w+?>CGBC8ZpZ!447?^CZB^wB:7Zw4C7C+GZ+?CwZCwG+848wG747pwp7^^w!+^Bm4CpdpRBw^>74C4^d4G+8Bd4!pdZ>ZC8G875w>w4G+7Gj>+7?C?B!47!J+p?!4w7!Bp^88>+G^44>7C!4dZ?4GBGw8!8?B>?C8+ZCB7?87+C7Cw87!>BC4>7?7B^!4++>!84Gp7!4B7w+?wC>>787+?dZ477wC!Z?8+.p+^BG4w7ZZGwG77GG>8p+!Cd^d!?>YX>B^w+Z+w^Y8?:^Cw?G2dG+d7>G8?+pB>?G7pCG^wwpG+4+8pp+dC?p7>Zp>n^?+^Bp4pp4C!dpw?++#4>788pd!7?>cZZp^Z8G8mG7d?umC>CpwB+<C?>tp!88B>^^J8GBG?8Z+8G^>8p7p4dZ>G+4BZB^ph!4BG447w7d>Z4!!dGGBBp>7Cd8?7c>+L>vp77CBw?B7+ZB^^^Zg7!p4B4dp7d>wZ7pCZ>G>T+^Z?wUA>Z>^>8Zw^GpdZp>7!^#w?p^C?^B8CK^BBB7p77+dwwpwBG>4Z8p!ZdGdv7?+?^^>C8wZG487dZ!ddw>wCG>B8p!!B!7?!8!+>^7>^!Bd+^p7+!wd44pWw+!4C87!ZdZBwi++dC>w?8d+7??w+pp^88Bw>!!>Zp+!7B^?Z7dNk^Z4Zp4BUB>7+ZpGGw4pB!4d+p?pZdG?b7CCCZ88}+4GG>4pwpddZ>!2dU?GZ4w!+BGBBgd7Cdw8zx8+e4Z4wZ+dw>C+!!8>>8w+8!G?a?>ZZBC4G37G7dBwZZCZ!?Bw+n?C8>?8wGddBw+7?Cb^!ww+G4dp!!BdBB77?+4>1>>+?dv4p7eZ7^??G+G+!>B4+74B???7^7?>U4B+GZ!4Z7+Z7d^wZPd+U>Z?Z74dJd>=w!p^+w77^Gp4>p!7Z^Gw9/CGCC8p!7^B7d+?>+!^Z8Z+%!p>?8wZdBw>Z6?+7>gpK+8Z?4878CdZww^pdG8BB7CZ7Gw?774C^dw84pZBwB8?GZd^>^++9C4>?44pw!pw8rGIp>wpd_CBd4!7X!C^!8ww7C4d+p>!dZ+d>8!CZ>Z8XppB=?174p7^?>4+g+>4d7!b7d!487?!7^8>B+7!nB^p4ZwZCwd++Cw>+pZ8pB4^>7B-!C44Z+^GwBGp^7Zdd^Ggw+G4Zpp#?Bp4>7d!?^>>!+>!!?j7?+^d??BhCZ^>B>w7dZp?C?8Z>^a?w78C4>8p7-?Zdw>9CC^>^>+!CBp4>ppCYZ?w4pBGCG7pZ7+d^^+s>GBB8p!7pC!^>/Z7p^G^p!+CZ>BpC68dZ?p7d(^B+8>!ZGp4Z7G7<d4>?L>+C>^4B!8ZB>w7p!ZB^8^!^Bw4!?ZZ4CZ4+7wGG^GwZ*Cd4>Z?8+B^p8^+ZG^444GZ+Gr8GMd+FG^4!!Z!wdC?pCd^>8Z84BZ4Gp7CdCww+)G_p>G?+7^Bpdd7ZZ4dB?d#CGd4>wG7+^Z?pu!G!C?8d+CdC4Cw?Z?CC^wpGGB>Z7+!ZC4?4wZo?BC8G!Zd+447GZ>C!w>pG!wG>7^Z)Z>wd+!!7>!w8+?C748gd74^wwdH?G?GCpc+8d8488CCwC8>GppG7487?ZCd7wZwwGZB7?BZZB!?4wZC?>^47!CCL^Cw?CGd+^C+BG+>GpG8pB44dcdZdGp8B8d+7B44wZdC_w^+G!w4+?8:+C^w!p7CCC88VS7G?^^wdZ+G74+wdC4>^pB+7B44p?dZpG44++pC84G4pC+BZ?BfC!8>Z8p+dGwB7J+Zw^c8Lw>GG>!7!!!C>w+w!&4GG>d!!!??Z#S+d^w4^pwZZ4884!7Z^??74CBdZ?!+wC4^w?!ZGd!8L",
    "!!>>>4!p4CZ?C(CC+G78C+^!+4C47Z+C+w^8+G744wd!4BG?ZpdCGCp8G8pBw?d8CZdd!w17CG!G^?Z74dad>JdG!B78!h8G?^7p87Z^Gw(PCGCC88>+Bd+4Bw8Z^d>w49dB!>d8p!8d8?4?8C8>+>4+8!^44?4C+C48!+7ZB4?wdb?G!wwwdCpGd87!wCZ?^8C+^G78>8+!wdZp>7!^Ew?p^C?^B8CF^BBBw7pZ>d8w8wBG!^7p7+7CB?+7!CZ^RpA+5Gd4B7BZZZBwB94+Z>B4+!ZZZ?4wZGBd^w?+>C!4^7Z!wC",
    "wd8FBZ^G8^!BC7?G",
    "gZ>C44GpwB!B?7>CCB+wC77C8^+87pGdd?!7BCBZ7w?M^BB>^?7!4B??w7>Gd^>8_+7B74w47Z^d^8>a^!7>p??!wdw?>?wC^>^8G7ZdC4+7>9!>G?dn4G^^+pdZ!B??U8dCCGa>^7+B74>77Cd^?wpwdBZdop+Z+Bp^wsCCp^>wp!x+?4^7pZpd48!(pG?4+>4!GB8dd74",
    ">^>>d!k!!4w7BZ+dBw^wZG>Bp8874d!?w7dZw^p^>+7ZC4ww&Zpd^wp+BG4>8>BZ!!w?dweCG>y4^!BC+487}Z+Z>wCw8G!>8?GZZCp?478ZpCB^8!dG7B^wpZZ^^8o0CCB>G?Bp+BCd)/?7pddw78^BR48wCZ>GG>>p",
    "G4^B>w+w!dd+747wCZwB+BGZGBp>ZZBp?ZgGzl^8pZ!ZG7?d7ZC:^Bd7l?G!GwppppBpdw7>Yd>+8?+BG?484^ZG^?w?bdG7>?p^!p!d?w74<CG+>^87!+4?7GZ(dGwdw!C7Gw8pp4B^d^7!DB^w>C+Z!!?!?<Z^Zp8ZwwG+G4ppp^d+^G78Cd^!wd+>+C4)?7ZGZp?4F+Z8dpw?+pdN^ww4C8^Gw8+dGdBBpw7G^Zwpp?Cp^>8dJ?B>^!7!7Cd78+w>G>>8>pZ+Z4w!/7!B>??d7?C!?ww>g7dp^5+?GB>4p4pZdZ?d?^C?GCp!!8C448p^ZBB4w^pZG+GZpp!>B8?8?BCZB+ww84BGBpww!d^Y?opp!74G?pp?d!d?7pZ8^>?B7GG7^8w77^d7^^+BB+dpp++wB4>p7wfG^w>G!GC!478C+4dG8!p7+!4Q>87BB>^B7+77^4>Z7+Cw4G8G+ZGCw4pZ+l>d4_c>GG^w7>!>>!?Gp?+84C48ZG^!>>Up!4Bp?G!7!4?+?4CGd>8?u4G744pdZGZdwGw!+?^w84!^B+BZ7ZZdZ^w?8CGw4>w}ZGG+>G8wGBBZ8+8ZBp4>p8Z8ZBw",
    "p+CwG4pGppCw4d)uZ/Bp47!GZpB?7!7?d78!%?!8>!8wZEd d^77-^>??8+QGp^dp8Z^d+^Gfw+G4Cw+!CB4wC)^+wB^>!!(+8dBp8cBd7?w?pCCB>87p^BC4?pd!^dGw^+4ZBdCpGZZ^+?4?wCdBd>p!C!8?B7>+d^Z4?+!BGd4?>Z4C>^OyB27>8p^nGB^4Z7+!G^Z>?rwGw>>>w!^d^?G?^CC>!pn+>+?4?p77O^Z^w7!GCd>?p!pdpw!7^+>^>^?!BB+4G7Gppdpw!wZC747?G!?ZCwdp>Zw^4?Z+>BC>84GC9d??7<7.^4+8?!8B>4wsB7>^>w8wpB+B4p8+>^Lw!+ZZB^pw+Z>Z74p?iC?^Bw4+4+Z>7?p7Gd?^CIwG>d5pGR+CG^w-B+Edp8G37G!4Z?4!?d??^8BGHBB8+87B4dZ7dC4^Z4GQ4GC4^p^p8CBwdqdGC>F848Gd_?G77ZBGZwC+!Bu>>>?ZxCB?+?7C4GZ8d!4BZ^Gp4ZC^^w^w8!B>dpd!C!d?ZD+C7^^^4+4GpG7p?C?G+wd8_BZ^G8^!BC7?GuYZ>C+8w+dG?4?4CZGC7wGwpZ!^8?47pBd?>WZZ+Cd^88j+?Bd?r7dZp^Sw?G+G4p^ZZGe?ZppZwdnwppBB?dB7?CCC>?4(^GB^7>+!GZww+V4.7>G>7!C+pBG4wC!Cd?^",
    "^CGC^8C!CB+BC79C.d8^0Uw!?>44d!7Z^wB++!p>+ww+4Cp4wwCZG^Gw!pKG!4!pwp+B8^wJ!l?^dwpn4C?>^p?Z8G>^^Y8 4>Gpd87B7?Z?wC",
    "Bj4B+Z!o?!p8Z+^+d4vpCZ>C4Z!jB+^^{wZC^G>!+4!Z?+7w+>dw?dsGZ>>d8+7BBd?G_ZZwZp8Z5dG^>d?pC!B4??w^GRG>pdZ!C7?!p8Z?B7w8Od!w>8p8!4C^?>v>CBBC8w!BB+4B7^7Zd7>p+!+w>>4^ZKZ>wd+!!7>!w8+?C74844Z8^dw!TdG>GCpV78dG?774Z7>+^w+CZG487dZ!ddw>wCG+4dpd!Zd??dyCC4CZw88>!BB8?C7B^?wZ#?GG>G>dZ!ddw+7^74^G8dw7G?4?p^p?ddwd5Crd4G88!GB747?+Cw>+8?+!+C>77+p>d>?8?pC>B^4j!B!7?!78!pBBwd&!C!4Z4{Z?dB?4Y4"
  ],
  "possible_keyword_mappings": {
    "8CBZBB4p": "function",
    "CCZ7w?p4": "function",
    "<GC7^4w7": "function",
    "ZB^Bd7o?": "function",
    "4dBnG6BG": "function",
    "ZpG8^7+Z": "function",
    "wCGC>^4+": "function",
    "^CGwGlpC": "function",
    "+G^6>wpZ": "function",
    "G>C?X7BC": "function",
    "nwCwCpp>": "function",
    "7Z+?^B8^": "function",
    "8?4w+Z#^": "function",
    "+GGw++CG": "function",
    "C^C^4w?G": "function",
    "(CC7w?+u": "function",
    "GdC7phGB": "function",
    "%4?4pZCd": "function",
    "7^>^V,++": "function",
    "C+7?Gw4C": "function",
    "+dZ^7??3": "function",
    "VBd88pBB": "function",
    "BZY??wCG": "function",
    "wd44pWw+": "function",
    "2dU?GZ4w": "function",
    "(pG?4+>4": "function",
    "DB^w>C+Z": "function",
    "B>??d7?C": "function",
    "74G?pp?d": "function",
    "7^+>^>^?": "function",
    "wZC747?G": "function",
    "C+pBG4wC": "function",
    "pGG4>p(O": "function",
    "9ZGZ>E4p": "function",
    "Bdf4?7IC": "function",
    "?7CdG=8p": "function",
    ">CB4>pCZ": "function",
    "ZB^Bd7/d": "function",
    "7+B+>8p8": "function",
    "pGZ8d(^4": "function",
    "BG^>7wCZ": "function",
    "PpZ8wCw8": "function",
    "1Z>^+pp7": "function",
    "BdBB77?C": "function",
    ":>d4w4fd": "function",
    "7>Z^G8w?": "function",
    "7jG8^Rp7": "function",
    "+8Z4>88^": "function",
    "??C?^7>i": "function",
    "BdB?Z?BC": "function",
    "??+8BC8^": "function",
    "7GCwCpCG": "function",
    "7C^Cw^8B": "function",
    "+G+BC4?7": "function",
    "#GGBB88p": "function",
    "wGBt>?87": "function",
    "ZdC?Z84G": "function",
    "C44p?C75": "function",
    "8C+wG>^8": "function",
    "ZCZd8w7G": "function",
    "ddd?C?dC": "function",
    "4+8487+7": "function",
    "wdw?wp+G": "function",
    ">BB^C7pZ": "function",
    "o8G+4+>4": "function",
    "VdvB>7pZ": "function",
    "3BGBC787": "function",
    "+B^^87?G": "function",
    "O3Gd>+8C": "function",
    "+BB>?++w": "function",
    "pCGwdup+": "function",
    "7d8?B?^C": "function",
    "+?^?4?+>": "function",
    "aBGpBC?+": "function",
    "?wM7CCG^": "function",
    "^BwB8w^+": "function",
    "BB??8?^C": "function",
    "CB8>7Z^d": "function",
    "C>8>7Z^d": "function",
    "BBR?B7pC": "function",
    "C+>4G7GZ": "function",
    "??4?>{ZG": "function",
    "?4GZ^8pZ": "function",
    "N8G+4+>4": "function",
    "-ZC48d88": "function",
    ";+!ZCGBd": "function",
    "78w!8?Ap": "function",
    "dww+p+>4": "function",
    "d8++++Z4": "function",
    "4w7!++w4": "function",
    "?dpN>Cd>": "function",
    "C+Z+?G44": "function",
    ">Z8+!!B+": "function",
    "d!!Z!4!7": "function",
    "dd?+CHZ7": "function",
    "GV3Gp4n8": "function",
    "474+BGw4": "function",
    "BC4?7GBC": "function",
    "wwwZpB+4": "function",
    "w!78C!>C": "function",
    "7!CZ4?7(": "function",
    "Cww+BGBG": "function",
    "G74BZZBd": "function",
    "4?p84B>B": "function",
    "w8Z!BZw4": "function",
    "?awf+w4+": "function",
    "B7?778_p": "function",
    "?pEGZdBp": "function",
    "_d?Qd!BG": "function",
    "!ZBB7C78": "function",
    "BGdC7GC?": "function",
    "?wd+7G?4": "function",
    "BC?C7+m7": "function",
    "GCdd54GC": "function",
    "C+?8FCC.": "function",
    "8?7CZCww": "function",
    "!eWZ4>K8": "function",
    "B++!74Cp": "function",
    "d!?>YX>B": "function",
    "Z?wUA>Z>": "function",
    "GpdZp>7!": "function",
    "Gp4>p!7Z": "function",
    "B7d+?>+!": "function",
    "8>B+7!nB": "function",
    ">wp!x+?4": "function",
    ">??8+QGp": "function",
    "OyB27>8p": "function",
    "Z4GQ4GC4": "function",
    "Sw?G+G4p": "function",
    "dwpn4C?>": "function",
    "+>4+wG.?": "function",
    "Z+w+_GoB": "function",
    "4>7dCGdp": "function",
    "F+G+B>B4": "function",
    "p+B+7487": "function",
    "?84p!d8?": "function",
    "?mBZZd78": "function",
    "Cw++wG+>": "function",
    "p8G+4B?4": "function",
    "Fwpe!B(d": "function",
    "g+aGCGGp": "function",
    ">8BpdB7d": "function",
    ">78>+wB8": "function",
    "!4d+pGZ?": "function",
    "Zdp+>Gp4": "function",
    "??3+7CA?": "function",
    "U??77C7C": "function",
    "B7dGw!nC": "function",
    "dp+8CBC4": "function",
    "4?wZ78C_": "function",
    "Bw?874GG": "function",
    "d+dZ7wZw": "function",
    "p7wK4Gd4": "function",
    "C8w+CZ?4": "function",
    "BZ4d7d7I": "function",
    "?>>pBZd4": "function",
    "dwZ+ZGZd": "function",
    "B8wG7>G+": "function",
    "Z?8:(G+B": "function",
    "ww7!5Zd4": "function",
    "787+wZ44": "function",
    ">IG!B>pp": "function",
    "?8?+?CY?": "function",
    ">w>8!GCd": "function",
    "Cw+!+G+>": "function",
    "B8?p7?G?": "function",
    "?+CB?4Gp": "function",
    "BB?ApwZ7": "function",
    "!GBdpd!C": "function",
    "?8-!!+8?": "function",
    "BpB7747p": "function",
    "G?dTB>Gw": "function",
    "?8ZBGBk4": "function",
    "G8pGZ>dB": "function",
    "+cBGw>w8": "function",
    "Gdp+>Gp4": "function",
    "?(DCCCG8": "function",
    "!wB+B,74": "function",
    "4 >Gr>C?": "function",
    "ZZ^fw": "false",
    "dCB7#": "false",
    "CB^78": "false",
    "7d^w4": "false",
    "B+^^w": "false",
    "C78Zp": "false",
    "C>Z>G": "false",
    "ZB??+": "false",
    "eC+7>": "false",
    "C^>do": "false",
    "1c+wB": "false",
    "dB7?B": "false",
    "G>78Z": "false",
    "d^^w7": "false",
    "C4??8": "false",
    "?^^4p": "false",
    "C^Cw>": "false",
    "84BC^": "false",
    "vCp4p": "false",
    "4dw?^": "false",
    "?BpwC": "false",
    "Cp?8+": "false",
    "+B4{7": "false",
    "G>^84": "false",
    "C4?p^": "false",
    "ZB4_1": "false",
    "4444G": "false",
    "{>^87": "false",
    "4?Zad": "false",
    "BG^4C": "false",
    "w4B8d": "false",
    "GB>?e": "false",
    "?>B4C": "false",
    "pd?wp": "false",
    "B^7?>": "false",
    "84Gp7": "false",
    "ZB^8^": "false",
    "C>w+w": "false",
    "w17CG": "false",
    "7>p??": "false",
    "4Bp?G": "false",
    "7?d78": "false",
    "B>dpd": "false",
    "?>44d": "false",
    "w>8p8": "false",
    ">B8?C": "false",
    "Z4+47": "false",
    "wB<w^": "false",
    "^Bw?Z": "false",
    "+^7pB": "false",
    "8G+pw": "false",
    "44X4>": "false",
    "Z>?w:": "false",
    "wBwxC": "false",
    "8>?pC": "false",
    "Z>ZpZ": "false",
    "4>^p7": "false",
    "uWZC>": "false",
    "Z>Z?Z": "false",
    "wdG4d": "false",
    "CCw?8": "false",
    "74Z4?": "false",
    "+?G47": "false",
    "8>ppp": "false",
    "BB>>w": "false",
    "^C}?+": "false",
    "+CZC4": "false",
    "8dp7^": "false",
    ">>4?>": "false",
    "?>wpw": "false",
    ">>dpp": "false",
    "8w8dd": "false",
    "74u7D": "false",
    "^>Bp8": "false",
    "C>C8:": "false",
    "CG^7Z": "false",
    "8dB4^": "false",
    "pCB?d": "false",
    "d>d8Z": "false",
    "CdGp7": "false",
    "4>?p?": "false",
    "^B4p4": "false",
    "BBp>8": "false",
    "B>pp^": "false",
    "7497-": "false",
    ">>>8G": "false",
    "^CL?+": "false",
    "Z>C?Z": "false",
    "C>d?p": "false",
    "_C77>": "false",
    "B>d?B": "false",
    ">B?p?": "false",
    "Z7d^p": "false",
    "74071": "false",
    "CCZ78": "false",
    "NNd^w": "false",
    "wdC7d": "false",
    "dG>7C": "false",
    "B>Cp?": "false",
    "GBdpd": "false",
    "74d8?": "false",
    "x>Z4B": "false",
    "CB^>^": "false",
    ">4+p?": "false",
    "Z+^4I": "false",
    "G>8>B": "false",
    "wdZ?Z": "false",
    "Gp4Z+": "false",
    "7C8wp": "false",
    "Zp>B8": "false",
    "Z+d<w": "false",
    "?B+7B": "false",
    "?BG7C": "false",
    "ZCwZw": "false",
    "+pdB?": "false",
    "ZG>pp": "false",
    "??4?w": "false",
    "+>78>": "false",
    "+pB+4": "false",
    "0d>S8": "false",
    "d>B4h": "false",
    "+>d48": "false",
    "BpwB{": "false",
    "p9GE4": "false",
    "8WGC7": "false",
    "d77C>": "false",
    "Gw8+C": "false",
    "p77C7": "false",
    "d?>Gw": "false",
    "+7G>p": "false",
    "+7!7d": "false",
    "dCwwS": "false",
    "BwB87": "false",
    "CB>88": "false",
    "p+>Z>": "false",
    "8)BZ>": "false",
    "d?zpG": "false",
    "8w7Cd": "false",
    "Z7!+G": "false",
    "77CB?": "false",
    "?7H?d": "false",
    ">7p>G": "false",
    "G74w+": "false",
    "477>B": "false",
    "Cw4+B": "false",
    "twd+C": "false",
    "88Z+p": "false",
    "+ZZGd": "false",
    "6pG4w": "false",
    "d7w>Z": "false",
    "G7BCB": "false",
    "4wp7p": "false",
    "?7Z!d": "false",
    "C8++G": "false",
    "w>G+>": "false",
    "!pp77": "false",
    "D8GG!": "false",
    "d?4h+": "false",
    "+8?Cs": "false",
    "Gw>CG": "false",
    "84!+B": "false",
    "?7BC7": "false",
    "+>!{>": "false",
    "Z?d47": "false",
    "4+p!!": "false",
    "88XZ+": "false",
    "/dCGd": "false",
    ">?p?!": "false",
    "Cp?!7": "false",
    "d78(B": "false",
    "wp4v!": "false",
    "C?5!7": "false",
    "+BC44": "false",
    "7??3!": "false",
    "+wG7G": "false",
    "4B84Z": "false",
    ">74C4": "false",
    "88>+G": "false",
    "w+Z+w": "false",
    "GwBGp": "false",
    ">/Z7p": "false",
    "7p87Z": "false",
    "wsCCp": "false",
    "G78Cd": "false",
    "ww4C8": "false",
    "ZBB4w": "false",
    "B7+77": "false",
    "Gp4ZC": "false",
    "wJ!l?": "false",
    "w78C8": "false",
    "4w7!B": "false",
    "8B.4B": "false",
    "37++0": "false",
    "C7>!Z": "false",
    "p-BZ4": "false",
    "43!Cw": "false",
    "C+dZ>": "false",
    "78w!7": "false",
    "+pp7!": "false",
    "?7!ZC": "false",
    "rpw7j": "false",
    "8dG7B": "false",
    "Rp7!w": "false",
    "!BG4?": "false",
    "gp7!w": "false",
    "C7GCG": "false",
    "Hp?78": "false",
    "d7wqa": "false",
    ">w7m!": "false",
    "Z7?+Z": "false",
    "p7pZ4": "false",
    "C7VZc": "false",
    "=p?7+": "false",
    "B+G+?": "false",
    "+++RZ": "false",
    "zwK8>": "false",
    "474ZB": "false",
    "87bBZ": "false",
    "44Z+Z": "false",
    "B7B+B": "false",
    "+!m84": "false",
    "47?C?": "false",
    "G7ZC4": "false",
    "878+8": "false",
    "C7pZ!": "false",
    "Z7CCC": "false",
    "4wwCw": "false",
    "d77+d": "false",
    "Z7C+Z": "false",
    "C7d+p": "false",
    "!7Z+!": "false",
    "87?G!": "false",
    "ZGGZw": "false",
    "d7GCw": "false",
    "4#!+4": "false",
    "7z(+7": "false",
    "C4B48": "false",
    "C7G+C": "false",
    "+nCw>": "false",
    "BwB8w": "false",
    "d?8Gw": "false",
    "G7CZ+": "false",
    "+7?!7": "false",
    "?7?Zd": "false",
    "p8G7w": "false",
    "Cp7ZW": "false",
    "?7?+?": "false",
    "d8+f!": "false",
    "87p+8": "false",
    "pRBGH": "false",
    "C!BZp": "false",
    "4I!Cw": "false",
    "!?d??": "false",
    "7wZ7Z": "false",
    "!?!C8": "false",
    "B+G+d": "false",
    "84B>B": "false",
    "wC+wG": "false",
    "wd)BG": "false",
    "+7Z++": "false",
    "Z?!C8": "false",
    "4+4>!": "false",
    "G??G=": "false",
    "8+w+w": "false",
    "pw!7!": "false",
    "w?wCG": "false",
    "BG47p4": "repeat",
    "dBG?U7": "repeat",
    ">?p#BG": "repeat",
    "+pC8pW": "repeat",
    "GB4B>7": "repeat",
    "??d?p+": "repeat",
    ">dd?t?": "repeat",
    "+>+>dZ": "repeat",
    "^BwB8I": "repeat",
    "G+G+^d": "repeat",
    "8Cp>GP": "repeat",
    "84u?wX": "repeat",
    "hBG?d?": "repeat",
    "_dB?pY": "repeat",
    "WG?4o7": "repeat",
    "4dCYZz": "repeat",
    "P4>BpB": "repeat",
    "8G8?>p": "repeat",
    "GB4Z/7": "repeat",
    "G44w+p": "repeat",
    "p^D8GG": "repeat",
    "CdB>G7": "repeat",
    "^w4pS^": "repeat",
    "Z7C0^d": "repeat",
    "BB48rL": "repeat",
    "ZSBd4?": "repeat",
    "8^K>Bz": "repeat",
    "G^8Cp>": "repeat",
    "++>+>w": "repeat",
    "B^>wC+": "repeat",
    "CG>Z?w": "repeat",
    "+Z>B4Z": "repeat",
    "kG>4>w": "repeat",
    "BC?4>7": "repeat",
    "+>^7>^": "repeat",
    "^Z8Z+%": "repeat",
    "dZ+d>8": "repeat",
    "&4GG>d": "repeat",
    "wdZp>7": "repeat",
    "+?^w84": "repeat",
    "GZpB?7": "repeat",
    "GCd>?p": "repeat",
    "pBBwd&": "repeat",
    "C?8?+7": "repeat",
    "_4^ww+": "repeat",
    "sG?4x7": "repeat",
    "^?C8ZC": "repeat",
    "kG>+87": "repeat",
    "dBCBd7": "repeat",
    "BBwC88": "repeat",
    "8+w?d7": "repeat",
    "B&4?w^": "repeat",
    "Cd>78Z": "repeat",
    "CB>B^7": "repeat",
    "GZ>o75": "repeat",
    "7Bw^43": "repeat",
    "C^G+87": "repeat",
    "G8>^8w": "repeat",
    "Cp^78w": "repeat",
    "CGp4+7": "repeat",
    "BG4?^7": "repeat",
    "pwZw8C": "repeat",
    "nC^Cw+": "repeat",
    "??d8B+": "repeat",
    "Gd>4p?": "repeat",
    "C8>7>d": "repeat",
    "^B8d+B": "repeat",
    "CB>d8C": "repeat",
    "7BC4?8": "repeat",
    "Z7>&Y+": "repeat",
    "8ZC?3w": "repeat",
    "CZwpp8": "repeat",
    "B44pdC": "repeat",
    "GB>?>?": "repeat",
    "dB4??8": "repeat",
    "4^G?4+": "repeat",
    ">?B??C": "repeat",
    ">8p+Z+": "repeat",
    "+ZdwMy": "repeat",
    "B%>>>?": "repeat",
    "GZ>tp?": "repeat",
    "ZBZ^^a": "repeat",
    "GBG^4#": "repeat",
    "+4^>wB": "repeat",
    "+GB4pp": "repeat",
    "pCB?G7": "repeat",
    "w4B84Z": "repeat",
    "G>^.7^": "repeat",
    "I8>^?^": "repeat",
    "?d+4pp": "repeat",
    "Gd4>4C": "repeat",
    "B+>p7>": "repeat",
    "p^B?4+": "repeat",
    "4BC?46": "repeat",
    ">+wB18": "repeat",
    "CB?7^C": "repeat",
    "B8>>*G": "repeat",
    "CBCd8w": "repeat",
    "^?>{+S": "repeat",
    "pG8pG7": "repeat",
    "Zpw^+8": "repeat",
    "CdC?C+": "repeat",
    "dC^+w^": "repeat",
    "w???45": "repeat",
    "Gw>rp+": "repeat",
    "^?88+C": "repeat",
    "d8>>87": "repeat",
    "QdB?p_": "repeat",
    "GC87Z+": "repeat",
    "C4C4pX": "repeat",
    "dww>77": "repeat",
    "B>6dZ7": "repeat",
    "+?4?wC": "repeat",
    "dG4?Zp": "repeat",
    "w!G+G+": "repeat",
    "CL?C7G": "repeat",
    "F4w!8C": "repeat",
    "4w>mpB": "repeat",
    "?81ZwG": "repeat",
    "47?+Zw": "repeat",
    "ZwwCpG": "repeat",
    ">#7878": "repeat",
    "w7!>dC": "repeat",
    ">TB++G": "repeat",
    "7?lZB?": "repeat",
    "?8zG!8": "repeat",
    "Zwd7wG": "repeat",
    "C8B!G>": "repeat",
    "W?ZwkJ": "repeat",
    "8?p!4!": "repeat",
    "87!ddw": "repeat",
    "?w7+Z8": "repeat",
    "ppp7dZ": "repeat",
    "8pw_CB": "repeat",
    "7pZ+4C": "repeat",
    "wB77C4": "repeat",
    "4c7BZ8": "repeat",
    "?Z7dNk": "repeat",
    "47!CCL": "repeat",
    "8!dG7B": "repeat",
    "4Z7+!G": "repeat",
    "p?Z8G>": "repeat",
    "B?CTZZ": "repeat",
    "pw4j?G": "repeat",
    "w4+8Bd": "repeat",
    ">w8wpG": "repeat",
    "%>dOw!": "repeat",
    ">w74+!": "repeat",
    "ww>pdG": "repeat",
    ">++7B4": "repeat",
    ">7wCZ!": "repeat",
    "4Cppp>": "repeat",
    "d?w+CC": "repeat",
    "?Z?>GC": "repeat",
    "?w4+?G": "repeat",
    "ww>wwG": "repeat",
    "wCpZG!": "repeat",
    ">8>!Z+": "repeat",
    "w>4pdZ": "repeat",
    "dwZ#8n": "repeat",
    "?p?>GC": "repeat",
    "?w7>G>": "repeat",
    "B8d+B!": "repeat",
    "dH7ZCZ": "repeat",
    "?G+KCG": "repeat",
    "p8ZdBp": "repeat",
    "?7wC4>": "repeat",
    "?47CCw": "repeat",
    "d+7>+w": "repeat",
    "d87?58": "repeat",
    "?>vdGG": "repeat",
    "BpCZhd": "repeat",
    "8>!>BB": "repeat",
    "7785?G": "repeat",
    "p!8dB4": "repeat",
    ">44!CG": "repeat",
    "8BpCZ+": "repeat",
    "?>7BGB": "repeat",
    "B4p4!d": "repeat",
    "4C44CG": "repeat",
    "?C8GC7": "repeat",
    "48wZCZ": "repeat",
    "84w+wG": "repeat",
    "B?4}?C": "repeat",
    "?8!7>B": "repeat",
    "B+ppZp": "repeat",
    "7wwp4G": "repeat",
    "w_7wC>": "repeat",
    "wdwdZp": "repeat",
    "4>C8?B": "repeat",
    "Cd83>!": "repeat",
    "?!kZC4": "repeat",
    ">B?8C!": "repeat",
    "78Cddp": "repeat",
    "G>C8?B": "repeat",
    "Cd8o>!": "repeat",
    "j??w+#": "repeat",
    "+8e%+G": "repeat",
    "?7Cw>B": "repeat",
    "884!+B": "repeat",
    ">8p7p4": "repeat",
    "B+7?ZC": "repeat",
    "B7pp78": "repeat",
    "(+ZG8>": "repeat",
    "?!p8Z!": "repeat",
    "B>7B7?": "repeat",
    "vGBp": "true",
    ">?pB": "true",
    ">88?": "true",
    "ZGp4": "true",
    "pwdo": "true",
    "CZ7?": "true",
    ">dGp": "true",
    "+BB8": "true",
    ">C^7": "true",
    "7dww": "true",
    "^w8Z": "true",
    "CZG?": "true",
    "G>?C": "true",
    "?4Gp": "true",
    ">B+w": "true",
    "b>??": "true",
    "pz7B": "true",
    "4Qpa": "true",
    "?^+>": "true",
    "8B8^": "true",
    ">wc7": "true",
    "8GpG": "true",
    "?w(i": "true",
    "?d+d": "true",
    "ZGGp": "true",
    ">?d&": "true",
    "Z+w?": "true",
    "G84d": "true",
    "GC8p": "true",
    "B>p4": "true",
    "8Z?w": "true",
    "dd?w": "true",
    ">B>7": "true",
    "8ZB?": "true",
    "?ZCw": "true",
    "dBC^": "true",
    "B>p?": "true",
    "4>pG": "true",
    "?B7?": "true",
    "J+p?": "true",
    "4++>": "true",
    "Cd^d": "true",
    "4C87": "true",
    ">Zp+": "true",
    "487?": "true",
    "?^>>": "true",
    "^Bw4": "true",
    "w>pG": "true",
    ">>>4": "true",
    ">d8p": "true",
    "4^7Z": "true",
    ">>Up": "true",
    "pdpw": "true",
    "4Z?4": "true",
    "4d?>": "true",
    "8B+?": "true",
    "wCww": "true",
    "8d+d": "true",
    "44?.": "true",
    "74?7": "true",
    ">C+?": "true",
    ">apd": "true",
    "7d+w": "true",
    ">ppp": "true",
    "8am>": "true",
    "wwdy": "true",
    "^88+": "true",
    "TGC?": "true",
    "Z>Cp": "true",
    "+GG4": "true",
    ">Gw+": "true",
    "+d^?": "true",
    "Cdw^": "true",
    ">74C": "true",
    ">>?p": "true",
    ">dp7": "true",
    "4Gp7": "true",
    "CdG?": "true",
    "^>?E": "true",
    "8<8p": "true",
    "G4Gp": "true",
    "C+8?": "true",
    ">ZpZ": "true",
    "8Cl8": "true",
    "B>B8": "true",
    "^ww7": "true",
    "GB>d": "true",
    "GGpd": "true",
    "W?Zw": "true",
    "?dZ?": "true",
    ">774": "true",
    "^d4d": "true",
    "G7?(": "true",
    "dBCw": "true",
    "BdCw": "true",
    "^8M>": "true",
    ">Bpd": "true",
    "?4+p": "true",
    "pGGw": "true",
    ">777": "true",
    "^>V?": "true",
    "?d>?": "true",
    "4C>7": "true",
    ">>>?": "true",
    "B?8w": "true",
    "?B>w": "true",
    "pZ4^": "true",
    "GGG>": "true",
    ">+8-": "true",
    "w78?": "true",
    "8dd?": "true",
    "4+p^": "true",
    "?d77": "true",
    "^Bd?": "true",
    ">48p": "true",
    "^?pZ": "true",
    "78+Z": "true",
    "7ZZ+": "true",
    "+dwl": "true",
    "fw!Z": "true",
    "w4Ip": "true",
    ">>G8": "true",
    ".BGC": "true",
    "w+Gd": "true",
    "p7w+": "true",
    "!CCR": "true",
    "!+8+": "true",
    "Zw8V": "true",
    ">8!G": "true",
    "!BGC": "true",
    "?+7B": "true",
    "w4pS": "true",
    "+7!!": "true",
    "p??9": "true",
    "w4Z>": "true",
    "B8_7": "true",
    "Z>++": "true",
    "88+d": "true",
    "*BCG": "true",
    "?pC7": "true",
    "7w?+": "true",
    "Bw7+": "true",
    "Cw)+": "true",
    "7pp!": "true",
    "G+!!": "true",
    "!>!Y": "true",
    "n7C?": "true",
    "p?7C": "true",
    "d8d7": "true",
    "8+BC": "true",
    "7?ZB": "true",
    "Y8?:": "true",
    ">8Zw": "true",
    "#w?p": "true",
    "B8CK": "true",
    "!Bd+": "true",
    "+w77": "true",
    "7Zdd": "true",
    "Ew?p": "true",
    "B8CF": "true",
    "wpZZ": "true",
    "7!DB": "true",
    "8w77": "true",
    "w84!": "true",
    "dp8Z": "true",
    "{wZC": "true",
    "p?j7": "true",
    "ww+!": "true",
    "p7Z+": "true",
    "!BZZ": "true",
    "8w4p": "true",
    "8?A!": "true",
    "Z3C8": "true",
    "B?4+": "true",
    "87QB": "true",
    "7GG_": "true",
    "pdCB": "true",
    "7!CG": "true",
    "+w!Z": "true",
    "7>d7": "true",
    "444+": "true",
    "Zd?w": "true",
    "?p!p": "true",
    "w?8C": "true",
    "ZeGZ": "true",
    "?8!?": "true",
    "pp4Z": "true",
    "G>C+": "true",
    "ppH8": "true",
    "?8?+": "true",
    "d8dC": "true",
    "zBGC": "true",
    "Z!B>": "true",
    "48+7": "true",
    "CBd7": "true",
    "mUZC": "true",
    "w?ZZ": "true",
    ">?E!": "true",
    "Zw8S": "true",
    "Cwd+": "true",
    "?+C+": "true",
    "pZp>": "true",
    "rBGC": "true",
    "+?Bw": "true",
    "w4Zw": "true",
    "7?8G": "true",
    "a!ZB": "true",
    "7Z+Z": "true",
    "pBZZ": "true",
    "Z?d3": "true",
    "G+pG": "true",
    "J?8+": "true",
    "??f!": "true",
    "78GG": "true",
    "47Z/": "true",
    "84>Z": "true",
    "ZZd+": "true",
    "7Gd>": "true",
    "K?+z": "true",
    "G>Zb": "true",
    "??8G": "true",
    "4wCd": "true",
    "!+8G": "true",
    "77B>": "true",
    "7ZZd": "true",
    "+dw9": "true",
    "p>>+": "true",
    "pqp+": "true",
    "487": "not",
    "?7w": "not",
    "+4>": "not",
    "G+G": "not",
    "wG8": "not",
    "4Zp": "not",
    ">^p": "not",
    ">74": "not",
    "d7^": "not",
    "78C": "not",
    "r84": "not",
    "d4w": "not",
    "^>+": "not",
    "Bww": "not",
    "^>8": "not",
    "B?7": "not",
    "8+p": "not",
    "8wC": "not",
    "w77": "not",
    "+7^": "not",
    "?8)": "not",
    "^88": "not",
    "^77": "not",
    "?pJ": "not",
    "4w7": "not",
    "b7d": "not",
    "7pC": "not",
    "ZGd": "not",
    "8L,": "not",
    "B78": "not",
    "p7+": "not",
    "pKG": "not",
    "C7?": "not",
    "Z7^": "not",
    "^Z4": "not",
    "?fp": "not",
    "B7d": "not",
    "n7+": "not",
    "ppZ": "not",
    "+pG": "not",
    "Z74": "not",
    "p7Z": "not",
    "w78": "not",
    ">4p": "not",
    "pd+": "not",
    "B7p": "not",
    "?47": "not",
    "w7C": "not",
    "Z4d": "not",
    "74C": "not",
    "G8>": "not",
    "+8G": "not",
    "GC>": "not",
    "p77": "not",
    "^48": "not",
    "847": "not",
    "8C+": "not",
    "w1+": "not",
    "87p": "not",
    "48p": "not",
    "}_Z": "not",
    ")CC": "not",
    ">48": "not",
    "7Z+": "not",
    "+FC": "not",
    "C8^": "not",
    "B8?": "not",
    "w8E": "not",
    "88+": "not",
    "47?": "not",
    "T7+": "not",
    ">dZ": "not",
    "p4B": "not",
    ">?8": "not",
    "BZ^": "not",
    "=+G": "not",
    "<8G": "not",
    "BZ4": "not",
    "dX4": "not",
    "^84": "not",
    "G7B": "not",
    "B87": "not",
    "p+p": "not",
    ">w7": "not",
    "C8>": "not",
    "p8Z": "not",
    "7wG": "not",
    "?87": "not",
    "wdw": "not",
    "?>7": "not",
    "wZ3": "not",
    ">!p": "not",
    "8B+": "not",
    ">Z8": "not",
    "C>>": "not",
    "GZ>": "not",
    "!pd": "not",
    "+BZ": "not",
    "DG)": "not",
    "?7C": "not",
    "d78": "not",
    "G78": "not",
    "w>G": "not",
    "C8d": "not",
    "84!": "not",
    "!>4": "not",
    "wp+": "not",
    ">8p": "not",
    "8+7": "not",
    "d74": "not",
    "wG+": "not",
    "48Z": "not",
    "V7G": "not",
    "7ZZ": "not",
    "iGG": "not",
    "?CZ": "not",
    "C+w": "not",
    "CZB": "not",
    "w!+": "not",
    "+ZG": "not",
    "BB>": "not",
    "8>a": "not",
    "77-": "not",
    "+wB": "not",
    "dGw": "not",
    "nGB": "not",
    "?G?": "not",
    "CGC": "not",
    "+Gd": "not",
    "wGw": "not",
    "GGd": "not",
    "dG8": "not",
    "p>K": "not",
    "BZ>": "not",
    "dZ?": "not",
    "!BG": "not",
    "AGv": "not",
    "dGd": "not",
    ">G8": "not",
    "8B{": "not",
    "w!w": "not",
    "pZ_": "not",
    "?Bp": "not",
    "+}>": "not",
    "7>+": "not",
    "+>Z": "not",
    "G>>": "not",
    "pdZ": "not",
    "p>U": "not",
    "!ZG": "not",
    "ZGZ": "not",
    "?Kp": "not",
    "dB4": "not",
    "!ZB": "not",
    "BGB": "not",
    "CZd": "not",
    "pZ+": "not",
    ":BG": "not",
    "?8C": "not",
    "4Z8": "not",
    "G>d": "not",
    "NZC": "not",
    "?Ip": "not",
    "ZZd": "not",
    "+8>": "not",
    "8Z+": "not",
    "!d!": "not",
    "d!w": "not",
    "74+": "not",
    "CB?": "not",
    "G4d": "not",
    "7CZ": "not",
    "4G4": "not",
    "zGt": "not",
    ">dGB^7ZZ+^": "loadstring",
    "Bpp7U7CZ>+": "loadstring",
    "7Z8wBw^G^>": "loadstring",
    "p8I>G_>4b>": "loadstring",
    "?d+pCoOCd>": "loadstring",
    "w49wGdpBpB": "loadstring",
    "?C4wC+dG>+": "loadstring",
    "c4Cp>+^4+B": "loadstring",
    "ZZB?>v>CBG": "loadstring",
    ">dC^48CC?>": "loadstring",
    "^>TB++G^pv": "loadstring",
    "7Zw?8<6Z>G": "loadstring",
    "TC^?pXGBGd": "loadstring",
    "?C7?C^?8zG": "loadstring",
    "vCdG7wGOw^": "loadstring",
    "?ppd+zB?pC": "loadstring",
    "4C8BZ7>78Q": "loadstring",
    "dB4>wF+Z+C": "loadstring",
    ">+EBBB>748": "loadstring",
    "cdwwdwpG44": "loadstring",
    "7?++Gd8854": "loadstring",
    "+dBd577A^>": "loadstring",
    ">BE++>d>4^": "loadstring",
    "pZG?B77C?>": "loadstring",
    "w?HGG?4Zpd": "loadstring",
    "wG?pC4Cw8^": "loadstring",
    "?8z?Z7>8>4": "loadstring",
    "?C74C+^48+": "loadstring",
    "p4ZdCpwZ87": "loadstring",
    "w8+?C748?Z": "loadstring",
    "CZ4^+7dqId": "loadstring",
    "??ZydCd8r4": "loadstring",
    "4dZ?4GBGw8": "loadstring",
    ">BC4>7?7B^": "loadstring",
    "8ww7C4d+p>": "loadstring",
    "^>/Z7p^G^p": "loadstring",
    "Zd+447GZ>C": "loadstring",
    "?4wZC?>^47": "loadstring",
    "w4+?8:+C^w": "loadstring",
    "^44?4C+C48": "loadstring",
    "wwwdCpGd87": "loadstring",
    "^d^?G?^CC>": "loadstring",
    "p8Z?B7w8Od": "loadstring",
    "KCG?47p77>": "loadstring",
    "w47pC7^p4j": "loadstring",
    "4?ZZ^%>dOw": "loadstring",
    "w+Z+B8?878": "loadstring",
    "847pdZdwpz": "loadstring",
    "wC?wCw>_>+": "loadstring",
    "+?+7G7B^>>": "loadstring",
    "wCRbCZ>8>a": "loadstring",
    "4BdB47BCC>": "loadstring",
    "G>^?G+pB+?": "loadstring",
    "ww7Zd:d77>": "loadstring",
    "p^7+Cwwh77": "loadstring",
    ">d?dZ+C+p4": "loadstring",
    "GdZw+747w^": "loadstring",
    "?w77G:Bd8^": "loadstring",
    "^BG^Z7?+Z^": "loadstring",
    "wCs?ZU4=w3": "loadstring",
    ">4/>CB4+8B": "loadstring",
    "pB?^>#d+>>": "loadstring",
    ">BBB>7GCG^": "loadstring",
    "pd+w??pCp>": "loadstring",
    "wBB+>wp/Z1": "loadstring",
    "7CB?dgdCCB": "loadstring",
    "4?ZBGB^88>": "loadstring",
    "ZC^w>r>GB>": "loadstring",
    ">77-B+^T7v": "loadstring",
    "BBG>^ZZBp?": "loadstring",
    "w+7pGp^p?Z": "loadstring",
    "?B7dJ7^GwB": "loadstring",
    "^7?8G^>Z8d": "loadstring",
    "77<C^?>+8p": "loadstring",
    "?pXpCpdZp8": "loadstring",
    "47k4Z7^B48": "loadstring",
    "dBC?77CZ?B": "loadstring",
    "?pwCG7>48p": "loadstring",
    "+>dw8+7G8^": "loadstring",
    "w+wCG7>48p": "loadstring",
    "8dp??+?C?>": "loadstring",
    "7ZZf>3wgDd": "loadstring",
    "+CB?B8BCCG": "loadstring",
    ">77cB+^ 7n": "loadstring",
    "w*}pCdC>8w": "loadstring",
    "BB+4B7^7Z^": "loadstring",
    "4dppZZ^Cw+": "loadstring",
    ">C6?PB4+8w": "loadstring",
    "w>79G7>w74": "loadstring",
    "8dp??+BC?>": "loadstring",
    ">4dwBZ^^^8": "loadstring",
    "+?+7G7Bdpp": "loadstring",
    "ZB8B^NZC^>": "loadstring",
    "w>pBGd4G7Z": "loadstring",
    ">^HZu>4C87": "loadstring",
    "?>CBC?w48^": "loadstring",
    "47:7Z7^B48": "loadstring",
    "wd8>COGGG>": "loadstring",
    "?CC+dGwB8Z": "loadstring",
    ">*c+G+^p4w": "loadstring",
    "d8WZkdC4>w": "loadstring",
    "dG1pC^>ppB": "loadstring",
    "pB>48787B^": "loadstring",
    "4d4?4p++ZB": "loadstring",
    "p4+wBCBdC>": "loadstring",
    "?d7?wwdCw4": "loadstring",
    "yBCZCB8+#7": "loadstring",
    "8ZwwZwwG^4": "loadstring",
    "wFy8d+>Cf>": "loadstring",
    "89_4B<4^pw": "loadstring",
    "4CZ{B8wG78": "loadstring",
    "7B?wZ77Cw>": "loadstring",
    ">Z8?BG47p4": "loadstring",
    "7dC>CC8.p7": "loadstring",
    "w4w?B.4C^8": "loadstring",
    "+444pp7dd?": "loadstring",
    "?6B+4B+>G!": "loadstring",
    "pwTCCG+>7p": "loadstring",
    "G4GGp+q*Z7": "loadstring",
    "U?G8pwp8BC": "loadstring",
    "p7!+?+7G7B": "loadstring",
    "BwB8I!C4>B": "loadstring",
    "B+4Zp87Cd#": "loadstring",
    "8>+!BZw?C?": "loadstring",
    "BdwG.NCC>C": "loadstring",
    "6>wpZ!7>w?": "loadstring",
    "!4+8ZGdBZ?": "loadstring",
    "Cw8wZG>4!>": "loadstring",
    "8pd+??7BZ?": "loadstring",
    "C8?8Z=w4+4": "loadstring",
    "!CZG?!7Z+?": "loadstring",
    "!vCdG7wGOw": "loadstring",
    "CZBG8C!CB?": "loadstring",
    "!>B+w!C4?p": "loadstring",
    "M8GGCpp!!?": "loadstring",
    "7GBC?Cy?G!": "loadstring",
    "CCB44B!8B8": "loadstring",
    "CZBBp?!?dC": "loadstring",
    "87d+dC?wCB": "loadstring",
    "!wG?pC4Cw8": "loadstring",
    "+u?sw>Z8d8": "loadstring",
    "!o!>?d&!+7": "loadstring",
    "Zdp+C!>>74": "loadstring",
    "Cw?+!GC8p!": "loadstring",
    "x?8w>GZ>88": "loadstring",
    "+8B7B47dC8": "loadstring",
    "GGBwp+78C+": "loadstring",
    "7+d7w4pdC4": "loadstring",
    "m?d+d4RwCd": "loadstring",
    "Bm4CpdpRBw": "loadstring",
    ">7B-!C44Z+": "loadstring",
    "7+B74>77Cd": "loadstring",
    "?w?bdG7>?p": "loadstring",
    "+4ZBdCpGZZ": "loadstring",
    "G8dw7G?4?p": "loadstring",
    "ZwZ88*8>Gw": "loadstring",
    ">8d!GGpBSp": "loadstring",
    "Z8C++Bw4+p": "loadstring",
    ",wdppG8>47": "loadstring",
    "+d+4?G7)ZC": "loadstring",
    "L4p78Z!dm?": "loadstring",
    "k+G7?4+ZCZ": "loadstring",
    "?+ZBrddpwH": "loadstring",
    "!GB+4?4BC+": "loadstring",
    "Cw>+ZC4Bd8": "loadstring",
    "8!1!74Z4?!": "loadstring",
    "7>+!GZw4?w": "loadstring",
    "!?w77G:Bd8": "loadstring",
    "8B!+dZ87pG": "loadstring",
    "!wd8?wpZGd": "loadstring",
    "+w?TBC?>8>": "loadstring",
    "+wB844MGZ4": "loadstring",
    "Z74pp?C>d?": "loadstring",
    "p!QG?>7p7p": "loadstring",
    "p8p+?+p447": "loadstring",
    "8w4!4G44+w": "loadstring",
    "pC+4?G7YZC": "loadstring",
    "+w?yBC?>8>": "loadstring",
    "!ZCZd8w7G!": "loadstring",
    "+wBwBGp8+x": "loadstring",
    "88>!>>dpp!": "loadstring",
    "B+>Z+4T88Z": "loadstring",
    "!d>d8Z!ZBZ": "loadstring",
    "8B!ZdCpGZZ": "loadstring",
    "+4+!4?pGZ?": "loadstring",
    "+>B>4B4>ZG": "loadstring",
    "8pB>???ZCZ": "loadstring",
    "BpK8ZBZ4d4": "loadstring",
    "+GdZ4Gpw++": "loadstring",
    "d4w!B!d4?p": "loadstring",
    "G88+GZw4dp": "loadstring",
    "pBZ7?.7pCw": "loadstring",
    "+JZCB7?wp?": "loadstring",
    "!ZBd?d?JZ?": "loadstring",
    "ZpGZw?77C7": "loadstring",
    "Z!!G?G7>74": "loadstring",
    "8pBpw!4G87": "loadstring",
    "ZZddwdwFGG": "loadstring",
    "+7BC?G7!G!": "loadstring",
    "7!!!>4dwBZ": "loadstring",
    "CBGC4+7!BZ": "loadstring",
    "Cw>7p>>8C+": "loadstring",
    "pBGBdBpG!!": "loadstring",
    "d+dG?GwpCc": "loadstring",
    "84!8ZC7B8?": "loadstring",
    "ZtBw?>77H*": "loadstring",
    "7+BG4G?7kZ": "loadstring",
    "!4++)+8B>7": "loadstring",
    "7dZ7dG,4Zp": "loadstring",
    "!?!C?7gCCp": "loadstring",
    "p:B4?Cp7ZC": "loadstring",
    "p+BC?dp>ZZ": "loadstring",
    "+ZZZBC4?Z-": "loadstring",
    "4wwC7>78wp": "loadstring",
    "8pd+8Bw?4c": "loadstring",
    "!BdC4847Z4": "loadstring",
    "pwp+8B844w": "loadstring",
    "ZG487dZ": "setfenv",
    "7^+dwl^": "setfenv",
    "dB7?C78": "setfenv",
    "8G>847d": "setfenv",
    "?Bw+CC>": "setfenv",
    "ppZCBB7": "setfenv",
    "GCG??wC": "setfenv",
    "p8d>>^8": "setfenv",
    "B?_++BC": "setfenv",
    "Z+d^47C": "setfenv",
    ">dd>B88": "setfenv",
    "8dcdZid": "setfenv",
    "M<4B48>": "setfenv",
    "+C+w+7B": "setfenv",
    "+GG?B7B": "setfenv",
    "GGppCpB": "setfenv",
    "%,4^<8>": "setfenv",
    "dBp?^84": "setfenv",
    ">BGw^pw": "setfenv",
    "p^_d?Qd": "setfenv",
    "C1C&wC8": "setfenv",
    "4ZZw+,w": "setfenv",
    "ZBB^Cp?": "setfenv",
    "dZ9?pR>": "setfenv",
    "wZG?>iG": "setfenv",
    "ZBdpK^G": "setfenv",
    "wBd^7pp": "setfenv",
    "^>B?45B": "setfenv",
    "p^Rd?,w": "setfenv",
    "Bd+^p7+": "setfenv",
    "8>>8w+8": "setfenv",
    "ww+G4dp": "setfenv",
    "7^8>B+7": "setfenv",
    "8ZB>w7p": "setfenv",
    "+wC4^w?": "setfenv",
    "d^Y?opp": "setfenv",
    "7Z^wB++": "setfenv",
    "^G8Z7wB": "setfenv",
    ">G8o+pG": "setfenv",
    "CG^Fwpe": "setfenv",
    "?dd>G7>": "setfenv",
    "_B?^C7>": "setfenv",
    "IGCi^pw": "setfenv",
    "?ZZ?CC8": "setfenv",
    "d^d?d7p": "setfenv",
    "4?Z^^^8": "setfenv",
    ">4pdZ^?": "setfenv",
    "pdpddqp": "setfenv",
    "w^BZ>pp": "setfenv",
    "4B7Bp7^": "setfenv",
    "pwB+BG7": "setfenv",
    "w7+w^74": "setfenv",
    "ZGwGp7Z": "setfenv",
    "pGp>?>p": "setfenv",
    "CZddp_q": "setfenv",
    ">GY8G+p": "setfenv",
    "p^>?p)G": "setfenv",
    "??C^>^p": "setfenv",
    "7GB84+7": "setfenv",
    "7^7?7xB": "setfenv",
    "p7C7d7>": "setfenv",
    "p^>?p/G": "setfenv",
    "?d+4p7d": "setfenv",
    ">GE8C+>": "setfenv",
    "+7d747w": "setfenv",
    "wB+dGC?": "setfenv",
    "4>wpwZB": "setfenv",
    "+G+>^?7": "setfenv",
    "pB?w>7?": "setfenv",
    ",>M?f+p": "setfenv",
    "BBZ^+7?": "setfenv",
    "C^C?C7?": "setfenv",
    "s>4wp+p": "setfenv",
    "GB4B>7Z": "setfenv",
    "+8?^w^Y": "setfenv",
    "8d7d4oG": "setfenv",
    "d>888pd": "setfenv",
    "CdqBZ7^": "setfenv",
    "?Z64GZG": "setfenv",
    "wdZ?d8?": "setfenv",
    "7BKC^p>": "setfenv",
    "4+84m7,": "setfenv",
    "7^+dw9^": "setfenv",
    "ZBd444Z": "setfenv",
    ">d?dBh?": "setfenv",
    "Zdd?8ZG": "setfenv",
    ">pp?Z+Z": "setfenv",
    "?GwCCCG": "setfenv",
    "7Cd^^d8": "setfenv",
    "9CG+>d>": "setfenv",
    "7p+8wBd": "setfenv",
    "G8++BG8": "setfenv",
    "GB4C88!": "setfenv",
    "B>pp78d": "setfenv",
    "GFG4pdp": "setfenv",
    "rGZ?wp+": "setfenv",
    "7Z+BGww": "setfenv",
    "4>4d&%d": "setfenv",
    "8!B+BW4": "setfenv",
    "37dZZBG": "setfenv",
    "C>w7ZBZ": "setfenv",
    ")?d?7jB": "setfenv",
    "q?dpePB": "setfenv",
    "+84+8B7": "setfenv",
    "?!?BZ8B": "setfenv",
    "gB>0>+8": "setfenv",
    "CB818++": "setfenv",
    "GC4+pdp": "setfenv",
    "?C7?Zdd": "setfenv",
    "7p+8wd8": "setfenv",
    "pZ7BBp?": "setfenv",
    "7CZ?dd?": "setfenv",
    "p>d!CZ4": "setfenv",
    "+wp!+dZ": "setfenv",
    "Cw+I7!B": "setfenv",
    "+7d+&Z4": "setfenv",
    "d??BhCZ": "setfenv",
    "Cw?CGd+": "setfenv",
    "w?+>C!4": "setfenv",
    "Zwpp?Cp": "setfenv",
    ">8dJ?B>": "setfenv",
    ">?B7GG7": "setfenv",
    "BC4?pd!": "setfenv",
    "Lw!+ZZB": "setfenv",
    "wpB>4Cp": "setfenv",
    "Bpddp!+": "setfenv",
    "+wwp>Cw": "setfenv",
    "d+w7+BB": "setfenv",
    "c8d??C8": "setfenv",
    "pSiG{>4": "setfenv",
    "!>B87CG": "setfenv",
    "!?BB?Cp": "setfenv",
    "Bp++BG8": "setfenv",
    "!w8p4C8": "setfenv",
    ">?G+>G8": "setfenv",
    "B87z7C8": "setfenv",
    "G4!IGCi": "setfenv",
    "pw!!1Z>": "setfenv",
    "Cw7pwC7": "setfenv",
    "B4Z1BC+": "setfenv",
    "GpG+GGw": "setfenv",
    "+7?!7dw": "setfenv",
    "7>i!Z!w": "setfenv",
    "w8w8GG8": "setfenv",
    "77dd4?7": "setfenv",
    "+>>Zwd+": "setfenv",
    "+p?>7CZ": "setfenv",
    "8pd>?C7": "setfenv",
    "C7GBpZp": "setfenv",
    "_?>??C?": "setfenv",
    "GpD+GGw": "setfenv",
    "4p4+4B+": "setfenv",
    "Gw48p4C": "setfenv",
    "+7!C!d7": "setfenv",
    "4wCudGB": "setfenv",
    "B7dB(Zb": "setfenv",
    ">Z+7Zpd": "setfenv",
    "8p8+8BC": "setfenv",
    "GG?ZpG7": "setfenv",
    "Zwd#41Z": "setfenv",
    "p!ZCd4p": "setfenv",
    "7w?+?G?": "setfenv",
    ">7!wZ+?": "setfenv",
    "f4GCG?8": "setfenv",
    "!88+!G>": "setfenv",
    "88784BB": "setfenv",
    "p8C+ZGG": "setfenv",
    "dp+>GC>": "setfenv",
    "7p8+7BB": "setfenv",
    "G?GC1>!": "setfenv",
    "C?CwdHp": "setfenv",
    "779B>Z>": "setfenv",
    "Z+ZB4p7": "setfenv",
    "ppz8?Bd": "setfenv",
    "!S!CB?7": "setfenv",
    "wwBGGGG": "setfenv",
    "Cw+wCC7": "setfenv",
    "Z44??8C": "setfenv",
    "+0GC>d>": "setfenv",
    "8L8C+?4": "setfenv",
    "+MGC>d>": "setfenv",
    "d77Z8Cd": "setfenv",
    "B4>Z+CB": "setfenv",
    "mCG+>d>": "setfenv",
    "+dB!4+p": "setfenv",
    "Bd?!7Cd": "setfenv",
    "B?44ZCZ": "setfenv"
  }
}

EXECUTION FLOW:
{
  "defined_functions": [
    "d",
    "__index",
    "__newindex"
  ],
  "d_calls": 25
}

DECODE RESULTS:
{
  "main_string_length": 3330,
  "lookup_table_length": 45654,
  "best_decode": [
    "pattern_mapping",
    "endnensluAtbZiDoootelseunioebDAmsdtuslntDiDbbtuoseZsAssunt5ZiDb8ldAndAooZtAZsDAellnZdtZtZAteVnintdleessnnndtAnslndKeisDtDoZEAdnonRbnsAnoNtiZtDiebtuisADu*SinttlniddbDlZbetsZeW(oDstAlAnZnoo9lDtlsbeu=DAu"
  ],
  "best_score": 20
}

SECURITY ASSESSMENT:
{
  "threat_level": "HIGH",
  "obfuscation_type": "MoonSec V3",
  "malicious_indicators": [
    "Heavy obfuscation to hide payload",
    "Environment manipulation (_ENV access)",
    "Dynamic code execution patterns",
    "String manipulation for deobfuscation",
    "Potential bytecode execution"
  ],
  "recommended_actions": [
    "DO NOT EXECUTE this file",
    "Isolate the file in a secure environment",
    "Report to security team if found in production",
    "Scan system for other similar files",
    "Check network logs for suspicious activity"
  ],
  "technical_details": {
    "protection": "MoonSec V3",
    "encoded_payload_size": "3330 characters",
    "lookup_table_size": "45654 characters",
    "decoder_function": "Found function 'd' with byte manipulation",
    "execution_method": "Likely uses getfenv() or loadstring()"
  }
}